<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Projet de veille technologique sur un capteur d'eau - Session 5 Technique Informatique - Cégep Garneau">
    <meta name="author" content="<PERSON><PERSON>">
    <title>TP1 Veille Technologique - Capteur d'Eau</title>
    <style>
        /* Style général inspiré de W3Schools */
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            background-color: #f1f1f1;
            line-height: 1.6;
            color: #333;
        }
        
        /* En-tête */
        .header {
            background-color: #282c34;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
        }
        
        .header p {
            margin: 10px 0 0;
            font-size: 1.2em;
        }
        
        /* Barre de navigation */
        .navbar {
            overflow: hidden;
            background-color: #04AA6D;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .navbar a {
            float: left;
            display: block;
            color: white;
            text-align: center;
            padding: 14px 20px;
            text-decoration: none;
        }
        
        .navbar a.right {
            float: right;
        }
        
        .navbar a:hover {
            background-color: #ddd;
            color: black;
        }
        
        .navbar a.active {
            background-color: #037a51;
            color: white;
        }
        
        /* Contenu principal */
        .main {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }
        
        .section {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .section:last-child {
            border-bottom: none;
        }
        
        .section h2 {
            color: #04AA6D;
            font-size: 1.8em;
            margin-bottom: 15px;
        }
        
        .section h3 {
            color: #282c34;
            font-size: 1.4em;
            margin-top: 25px;
        }
        
        /* Cartes pour les expérimentations */
        .card {
            background-color: #f9f9f9;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #04AA6D;
        }
        
        /* Tableaux */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #04AA6D;
            color: white;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        /* Images */
        .img-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .img-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        
        .img-container .caption {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }
        
        /* Citations et notes */
        .note {
            background-color: #ffffcc;
            border-left: 6px solid #ffeb3b;
            padding: 10px 15px;
            margin: 20px 0;
        }
        
        /* Pied de page */
        .footer {
            background-color: #282c34;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 40px;
        }

        .footer p {
            margin: 8px 0;
            line-height: 1.4;
        }

        .footer p:first-child {
            font-size: 1.1em;
            font-weight: bold;
        }
        
        /* Responsive design */
        @media screen and (max-width: 768px) {
            .navbar a {
                float: none;
                display: block;
                text-align: left;
            }
            
            .navbar a.right {
                float: none;
            }
            
            .main {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- En-tête -->
    <header class="header">
        <h1>Projet de Veille Technologique</h1>
        <p>Évaluation de la fonctionnalité d'un capteur de niveau d'eau</p>
        <p><small>Session 5 - Technique de l'informatique - Cégep Garneau</small></p>
    </header>

    <!-- Barre de navigation -->
    <nav class="navbar">
        <a href="#" class="active">Accueil</a>
        <a href="#usage">Usage proposé</a>
        <a href="#description">Description</a>
        <a href="#experimentations">Expérimentations</a>
        <a href="#avis">Avis</a>
        <a href="#sources" class="right">Sources</a>
    </nav>

    <!-- Contenu principal -->
    <main class="main">
        <!-- Section d'accueil -->
        <section class="section">
            <h2>Bienvenue sur mon projet de veille technologique</h2>
            <p>Ce site présente mon analyse et mes expérimentations avec un capteur d'eau, réalisées dans le cadre du cours de veille technologique.</p>
            <p>L'objectif est de valider l'utilisation d'un capteur d'eau pour la surveillance de niveau dans un réservoir et d'émettre un avis éclairé sur son potentiel.</p>
            <p><strong>Professeur :</strong> Jean-Philippe Boucher</p>
            <p><strong>Étudiant :</strong> Divin Koffi Sogbadji</p>
        </section>

        <!-- Section usage proposé -->
        <section class="section" id="usage">
            <h2>Usage proposé</h2>
            <p><strong>Hypothèse d'utilisation:</strong> Le capteur d'eau peut être utilisé pour surveiller le niveau d'eau dans un réservoir, déclencher une alarme en cas de débordement ou de niveau trop bas, et ainsi permettre une gestion automatisée de l'eau dans un contexte domestique ou agricole.</p>
        </section>

        <!-- Section description de l'objet -->
        <section class="section" id="description">
            <h2>Description de l'objet expérimenté</h2>
            
            <h3>Résumé des fonctionnalités</h3>
            <p>Le capteur d'eau testé est un module électronique capable de détecter la présence et le niveau d'eau grâce à des électrodes qui mesurent la conductivité du liquide.</p>
            
            <h3>Fabricant</h3>
            <p>Le module utilisé est un capteur générique largement disponible, semblable à ceux produits par DF Robot ou Seeed Studio.</p>
            
            <h3>Caractéristiques techniques</h3>
            <ul>
                <li>Tension d'alimentation: 5V DC</li>
                <li>Sortie: Signal analogique et numérique</li>
                <li>Dimensions: Environ 60mm x 20mm</li>
                <li>Principe de fonctionnement: Basé sur la conductivité de l'eau</li>
            </ul>
            
            <h3>Schématisation matérielle</h3>
            <div class="img-container">
                <img src="https://via.placeholder.com/600x300?text=Schéma+de+branchement+capteur+eau" alt="Schéma de branchement du capteur d'eau">
                <div class="caption">Schéma de connexion du capteur d'eau à une carte Arduino</div>
            </div>
        </section>

        <!-- Section expérimentations -->
        <section class="section" id="experimentations">
            <h2>Expérimentations</h2>
            <p>Plusieurs expériences ont été menées pour valider le fonctionnement du capteur d'eau et son utilisation potentielle.</p>
            
            <h3>Expérimentation 1: Calibration des valeurs minimales et maximales</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Déterminer les valeurs analogiques correspondant à un réservoir vide et plein.</p>
                <p><strong>Contexte:</strong> Le capteur a été branché à une carte Arduino Uno, avec alimentation 5V et lecture sur la pin analogique A0. Des mesures ont été prises à différents niveaux d'eau.</p>
                <div class="img-container">
                    <img src="https://via.placeholder.com/400x300?text=Photo+montage+expérimentation" alt="Photo du montage expérimental">
                    <div class="caption">Montage utilisé pour les expérimentations</div>
                </div>
                <p><strong>Résultats:</strong></p>
                <table>
                    <tr>
                        <th>Niveau d'eau</th>
                        <th>Valeur analogique</th>
                        <th>Sortie digitale</th>
                    </tr>
                    <tr>
                        <td>Absence d'eau</td>
                        <td>0</td>
                        <td>LOW</td>
                    </tr>
                    <tr>
                        <td>Niveau faible (1cm)</td>
                        <td>250</td>
                        <td>HIGH</td>
                    </tr>
                    <tr>
                        <td>Niveau moyen (3cm)</td>
                        <td>510</td>
                        <td>HIGH</td>
                    </tr>
                    <tr>
                        <td>Niveau maximal (5cm)</td>
                        <td>1023</td>
                        <td>HIGH</td>
                    </tr>
                </table>
            </div>
            
            <h3>Expérimentation 2: Stabilité dans le temps</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Vérifier si les valeurs restent stables sur une période prolongée.</p>
                <p><strong>Contexte:</strong> Le capteur a été laissé immergé pendant 24 heures, avec des mesures enregistrées toutes les 10 minutes.</p>
                <p><strong>Résultats:</strong> [Vos résultats ici]</p>
            </div>
            
            <h3>Expérimentation 3: Robustesse aux perturbations</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Tester la réaction du capteur à des perturbations (bulles, saleté).</p>
                <p><strong>Contexte:</strong> Des tests ont été réalisés avec de l'eau savonneuse et de l'eau contenant des particules en suspension.</p>
                <p><strong>Résultats:</strong> [Vos résultats ici]</p>
            </div>
        </section>

        <!-- Section avis -->
        <section class="section" id="avis">
            <h2>Avis sur la technologie</h2>
            
            <h3>Longévité</h3>
            <p>Le capteur montre une longévité correcte pour un usage occasionnel, mais peut être sujet à l'oxydation des électrodes en cas d'immersion prolongée.</p>
            
            <h3>Stabilité</h3>
            <p>Les mesures restent stables dans des conditions contrôlées, mais peuvent varier en fonction de la température et de la composition de l'eau.</p>
            
            <h3>Efficacité</h3>
            <p>Le capteur est efficace pour détecter la présence d'eau et estimer grossièrement le niveau, mais ne convient pas pour des mesures de précision.</p>
            
            <h3>Maintenabilité</h3>
            <p>Facile à remplacer et peu coûteux, ce qui compense sa durée de vie limitée dans des conditions difficiles.</p>
            
            <h3>Robustesse à la manipulation</h3>
            <p>Le module électronique est fragile et nécessite une manipulation cuidados. Les électrodes peuvent se plier ou se casser facilement.</p>
            
            <h3>Avantages et inconvénients</h3>
            <table>
                <tr>
                    <th>Avantages</th>
                    <th>Inconvénients</th>
                </tr>
                <tr>
                    <td>Prix très abordable</td>
                    <td>Sensible à l'oxydation</td>
                </tr>
                <tr>
                    <td>Facile à utiliser avec Arduino/Raspberry Pi</td>
                    <td>Mesures influencées par la minéralisation de l'eau</td>
                </tr>
                <tr>
                    <td>Détection fiable de la présence d'eau</td>
                    <td>Précision limitée pour la mesure de niveau</td>
                </tr>
            </table>
            
            <h3>Avis final</h3>
            <div class="note">
                <p>Ce capteur d'eau est une solution économique et satisfaisante pour des applications simples de détection de présence d'eau ou de surveillance approximative de niveau. Cependant, pour des applications nécessitant une précision élevée ou dans des environnaux difficiles, d'autres technologies (capteurs à ultrasons, capteurs capacitifs) seraient plus appropriées.</p>
            </div>
        </section>

        <!-- Section sources -->
        <section class="section" id="sources">
            <h2>Sources d'information</h2>

            <h3>Documentation et sites web</h3>
            <ul>
                <li><a href="https://docs.arduino.cc/hardware/mega-2560/" target="_blank">Arduino Mega 2560 (Documentation technique du fabricant)</a></li>
                <li><a href="https://www.arduino.cc/" target="_blank">Site officiel Arduino</a></li>
                <li><a href="https://www.instructables.com/Testing-Water-Level-Sensor-Arduino-UNO-Water-Level/" target="_blank">Instructables - Exemples montage + code</a></li>
                <li><a href="https://www.seeedstudio.com/" target="_blank">Seeed Studio</a></li>
                <li><a href="https://projecthub.arduino.cc/daryllqayyim/water-sensor-basic-13d33f" target="_blank">Arduino Project Hub - Water Sensor Basic</a></li>
                <li>Forums spécialisés en électronique (Arduino Forum, StackExchange)</li>
            </ul>

            <h3>Vidéos tutorielles consultées</h3>
            <ul>
                <li><a href="https://www.youtube.com/watch?v=n7WRi5U5lQk" target="_blank">"Arduino water level Sensor Tutorial"</a> - theGeekPub (12 mai 2019)</li>
                <li><a href="https://www.youtube.com/watch?v=6s4Sh8badNM" target="_blank">"[arduino] Mesurer le niveau d'eau avec Arduino"</a> - Comprendre l'électronique (16 juillet 2019)</li>
                <li><a href="https://www.youtube.com/watch?v=WzYmmu17svE" target="_blank">"Capteur de niveau d'eau analogique Arduino"</a> - Mr Automatisme et domotique (12 juin 2020)</li>
            </ul>
        </section>
    </main>

    <!-- Pied de page -->
    <footer class="footer">
        <p>© 2025 - Projet de veille technologique - Capteur d'eau</p>
        <p>Réalisé par <strong>Divin Koffi Sogbadji</strong> - Session 5 Technique de l'informatique</p>
        <p><strong>Cégep Garneau</strong> - Professeur: Jean-Philippe Boucher</p>
    </footer>

    <script>
        // Script simple pour la navigation
        window.onscroll = function() {stickyNav()};

        var navbar = document.querySelector(".navbar");
        var sticky = navbar.offsetTop;

        function stickyNav() {
            if (window.pageYOffset >= sticky) {
                navbar.classList.add("sticky");
            } else {
                navbar.classList.remove("sticky");
            }
        }

        // Gestion de l'état actif des liens de navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar a');
            const sections = document.querySelectorAll('.section');

            // Fonction pour mettre à jour le lien actif
            function updateActiveLink() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                // Si on est en haut de la page, activer "Accueil"
                if (window.pageYOffset < 100) {
                    current = '';
                }

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current ||
                        (current === '' && link.getAttribute('href') === '#')) {
                        link.classList.add('active');
                    }
                });
            }

            // Gestion du clic sur les liens
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Retirer la classe active de tous les liens
                    navLinks.forEach(l => l.classList.remove('active'));
                    // Ajouter la classe active au lien cliqué
                    this.classList.add('active');
                });
            });

            // Mettre à jour l'état actif lors du scroll
            window.addEventListener('scroll', updateActiveLink);

            // Initialiser l'état actif
            updateActiveLink();
        });
    </script>
</body>
</html>