<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Projet de veille technologique sur un capteur d'eau - Session 5 Technique Informatique - Cégep Garneau">
    <meta name="author" content="<PERSON><PERSON>">
    <title>TP1 Veille Technologique - Capteur d'Eau</title>
    <style>
        /* Style général inspiré de W3Schools */
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        
        body {
            margin: 0;
            padding: 0;
            background-color: #f1f1f1;
            line-height: 1.6;
            color: #333;
        }
        
        /* En-tête */
        .header {
            background-color: #282c34;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
        }
        
        .header p {
            margin: 10px 0 0;
            font-size: 1.2em;
        }
        
        /* Barre de navigation */
        .navbar {
            overflow: hidden;
            background-color: #04AA6D;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .navbar a {
            float: left;
            display: block;
            color: white;
            text-align: center;
            padding: 14px 20px;
            text-decoration: none;
        }
        
        .navbar a.right {
            float: right;
        }
        
        .navbar a:hover {
            background-color: #ddd;
            color: black;
        }
        
        .navbar a.active {
            background-color: #037a51;
            color: white;
        }
        
        /* Contenu principal */
        .main {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }
        
        .section {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .section:last-child {
            border-bottom: none;
        }
        
        .section h2 {
            color: #04AA6D;
            font-size: 1.8em;
            margin-bottom: 15px;
        }
        
        .section h3 {
            color: #282c34;
            font-size: 1.4em;
            margin-top: 25px;
        }
        
        /* Cartes pour les expérimentations - Design moderne */
        .card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            padding: 25px;
            margin-top: 25px;
            border-radius: 15px;
            border-left: 5px solid #04AA6D;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #04AA6D, #037a51, #04AA6D);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: -200% 0; }
            50% { background-position: 200% 0; }
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
        }
        
        /* Tableaux - Design moderne */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 25px 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            background: white;
        }

        table, th, td {
            border: none;
        }

        th, td {
            padding: 15px 18px;
            text-align: left;
        }

        th {
            background: linear-gradient(135deg, #04AA6D, #037a51);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tr:hover {
            background-color: #e8f5e8;
            transition: background-color 0.3s ease;
        }

        td {
            border-bottom: 1px solid #e9ecef;
        }
        
        /* Images et Médias - Design amélioré */
        .img-container {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }

        .img-container img {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .img-container img:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .img-container .caption {
            font-style: italic;
            color: #555;
            margin-top: 15px;
            font-size: 0.95em;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 4px solid #04AA6D;
            display: inline-block;
            max-width: 90%;
        }

        /* Vidéos - Design moderne */
        .img-container video {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            background-color: #000;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .img-container video:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }

        /* Galerie de médias */
        .media-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .media-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .media-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        /* Citations et notes - Design amélioré */
        .note {
            background: linear-gradient(135deg, #fff9c4, #fffbea);
            border-left: 6px solid #f59e0b;
            border-radius: 10px;
            padding: 20px 25px;
            margin: 25px 0;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.1);
            position: relative;
        }

        .note::before {
            content: '💡';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 1.5em;
            opacity: 0.7;
        }
        
        /* Pied de page */
        .footer {
            background-color: #282c34;
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 40px;
        }

        .footer p {
            margin: 8px 0;
            line-height: 1.4;
        }

        .footer p:first-child {
            font-size: 1.1em;
            font-weight: bold;
        }
        
        /* Animations et effets */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        .section:nth-child(2) { animation-delay: 0.1s; }
        .section:nth-child(3) { animation-delay: 0.2s; }
        .section:nth-child(4) { animation-delay: 0.3s; }
        .section:nth-child(5) { animation-delay: 0.4s; }
        .section:nth-child(6) { animation-delay: 0.5s; }

        /* Responsive design */
        @media screen and (max-width: 768px) {
            .navbar a {
                float: none;
                display: block;
                text-align: left;
            }

            .navbar a.right {
                float: none;
            }

            .main {
                padding: 15px;
            }

            .media-gallery {
                grid-template-columns: 1fr;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- En-tête -->
    <header class="header">
        <h1>Projet de Veille Technologique</h1>
        <p>Évaluation de la fonctionnalité d'un capteur de niveau d'eau</p>
        <p><small>Session 5 - Technique de l'informatique - Cégep Garneau</small></p>
    </header>

    <!-- Barre de navigation -->
    <nav class="navbar">
        <a href="#" class="active">Accueil</a>
        <a href="#usage">Usage proposé</a>
        <a href="#description">Description</a>
        <a href="#experimentations">Expérimentations</a>
        <a href="#avis">Avis</a>
        <a href="#sources" class="right">Sources</a>
    </nav>

    <!-- Contenu principal -->
    <main class="main">
        <!-- Section d'accueil -->
        <section class="section">
            <h2>Bienvenue sur mon projet de veille technologique</h2>
            <p>Ce site présente mon analyse et mes expérimentations avec un capteur d'eau, réalisées dans le cadre du cours de veille technologique.</p>
            <p>L'objectif est de valider l'utilisation d'un capteur d'eau pour la surveillance de niveau dans un réservoir et d'émettre un avis éclairé sur son potentiel.</p>
            <p><strong>Professeur :</strong> Jean-Philippe Boucher</p>
            <p><strong>Étudiant :</strong> Divin Koffi Sogbadji</p>
        </section>

        <!-- Section usage proposé -->
        <section class="section" id="usage">
            <h2>Usage proposé</h2>
            <p><strong>Hypothèse d'utilisation:</strong> Le capteur d'eau peut être utilisé pour surveiller le niveau d'eau dans un réservoir, déclencher une alarme en cas de débordement ou de niveau trop bas, et ainsi permettre une gestion automatisée de l'eau dans un contexte domestique ou agricole.</p>
        </section>

        <!-- Section description de l'objet -->
        <section class="section" id="description">
            <h2>Description de l'objet expérimenté</h2>
            
            <h3>Résumé des fonctionnalités</h3>
            <p>Le capteur d'eau testé est un module électronique capable de détecter la présence et le niveau d'eau grâce à des électrodes qui mesurent la conductivité du liquide.</p>
            
            <h3>Fabricant</h3>
            <p>Le capteur d'eau utilisé provient d'un kit complet Elegoo acheté sur Amazon. Le kit comprend une carte Arduino Mega 2560 Elegoo et divers composants électroniques incluant le capteur d'eau testé.</p>
            
            <h3>Caractéristiques techniques</h3>
            <ul>
                <li>Tension d'alimentation: 5V DC</li>
                <li>Sortie: Signal analogique (pin A0)</li>
                <li>Longueur du capteur: Environ 15 cm</li>
                <li>Plage de valeurs mesurées: 0 (sec) à 383 (totalement immergé)</li>
                <li>Valeur à mi-immersion: 185-200 (capteur plongé à moitié)</li>
                <li>Principe de fonctionnement: Basé sur la conductivité de l'eau</li>
                <li>Connexions testées: LED d'alerte (pin 10), Buzzer (pin 2)</li>
            </ul>
            
            <h3>Schématisation matérielle</h3>
            <div class="img-container">
                <img src="images/shema_inspi.jpg" alt="Schéma de branchement du capteur d'eau inspiré d'Arduino France">
                <div class="caption">Schéma de connexion du capteur d'eau à une carte Arduino (inspiré d'Arduino France)</div>
            </div>
        </section>

        <!-- Section expérimentations -->
        <section class="section" id="experimentations">
            <h2>Expérimentations</h2>
            <p>Plusieurs expériences ont été menées pour valider le fonctionnement du capteur d'eau et son utilisation potentielle.</p>
            
            <h3>Expérimentation 1: Test initial du capteur seul</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Déterminer les valeurs analogiques correspondant à un capteur à sec et immergé au maximum.</p>
                <p><strong>Contexte:</strong> Le capteur a été branché à une carte Arduino Mega 2560 Elegoo, avec alimentation 5V et lecture sur la pin analogique A0. Test simple pour valider le fonctionnement de base.</p>

                <div class="img-container">
                    <img src="images/montage.jpg" alt="Photo du montage expérimental réalisé">
                    <div class="caption">Montage réalisé pour les expérimentations</div>
                </div>

                <div class="img-container">
                    <img src="images/sans_eau.jpg" alt="Capture d'écran console Arduino - capteur à sec">
                    <div class="caption">Console Arduino montrant la valeur 0 quand le capteur est à sec</div>
                </div>

                <div class="img-container">
                    <video controls style="max-width: 100%; height: auto;">
                        <source src="images/video1.mp4" type="video/mp4">
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                    <div class="caption">Vidéo démonstration - Test initial du capteur</div>
                </div>
                <p><strong>Résultats obtenus:</strong></p>
                <table>
                    <tr>
                        <th>État du capteur</th>
                        <th>Valeur analogique mesurée</th>
                        <th>Observation</th>
                    </tr>
                    <tr>
                        <td>Capteur à sec</td>
                        <td>0</td>
                        <td>Aucune conductivité détectée</td>
                    </tr>
                    <tr>
                        <td>Capteur immergé à moitié (~7.5 cm)</td>
                        <td>185-200</td>
                        <td>Détection partielle de l'eau</td>
                    </tr>
                    <tr>
                        <td>Capteur totalement immergé (15 cm)</td>
                        <td>383</td>
                        <td>Valeur maximale atteinte</td>
                    </tr>
                </table>
                <p><strong>Conclusion:</strong> Le capteur fonctionne correctement avec une plage de valeurs de 0 à 383. La progression des valeurs (0 → 185-200 → 383) montre une réponse proportionnelle au niveau d'immersion.</p>

                <p><strong>Observation importante:</strong> Le capteur s'avère très sensible - il réagit même à l'humidité corporelle et aux variations environnementales. Plus la durée d'expérimentation augmente, plus on observe une variance dans les résultats, suggérant une sensibilité aux conditions ambiantes.</p>
            </div>
            
            <h3>Expérimentation 2: Système d'alerte avec LED et buzzer</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Valider le bon fonctionnement du matériel et l'authenticité des mesures en ajoutant des indicateurs visuels et sonores.</p>
                <p><strong>Contexte:</strong> Utilisation du kit Elegoo avec ajout d'une LED rouge (pin 10) et d'un buzzer (pin 2) pour déclencher une alerte lorsque le niveau d'eau dépasse un seuil défini.</p>
                <p><strong>Code Arduino utilisé:</strong></p>
                <div style="position: relative; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #1a202c, #2d3748); padding: 3px; border-radius: 12px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);">
                        <div style="background: #2d3748; border-radius: 10px; position: relative;">
                            <div style="background: linear-gradient(90deg, #04AA6D, #037a51); height: 3px; border-radius: 10px 10px 0 0;"></div>
                            <div style="padding: 8px 15px; background: #1a202c; color: #a0aec0; font-size: 0.8em; border-bottom: 1px solid #4a5568;">
                                <span style="color: #f56565;">●</span> <span style="color: #ed8936;">●</span> <span style="color: #48bb78;">●</span>
                                <span style="margin-left: 15px; font-family: monospace;">sketch_water_sensor.ino</span>
                            </div>
                <pre style="background: transparent; padding: 20px; margin: 0; border-radius: 0; overflow-x: auto; font-size: 0.9em; border: none;">
<code style="color: #e2e8f0; font-family: 'Courier New', monospace; line-height: 1.4;">
<span style="color: #68d391;">// Définition des pins de sortie</span>
<span style="color: #f687b3;">const</span> <span style="color: #63b3ed;">int</span> <span style="color: #fbb6ce;">RED_PIN</span> = <span style="color: #f6e05e;">10</span>;       <span style="color: #68d391;">// Pin pour la LED rouge d'alerte</span>
<span style="color: #f687b3;">const</span> <span style="color: #63b3ed;">int</span> <span style="color: #fbb6ce;">BUZZER_PIN</span> = <span style="color: #f6e05e;">2</span>;     <span style="color: #68d391;">//Pin pour le buzzer d'alerte</span>
<span style="color: #f687b3;">const</span> <span style="color: #63b3ed;">int</span> <span style="color: #fbb6ce;">Capteur_eau</span> = <span style="color: #f6e05e;">A0</span>;   <span style="color: #68d391;">// Pin analogue pour le capteur de niveau d'eau </span>

<span style="color: #63b3ed;">void</span> <span style="color: #81e6d9;">setup</span>() {
  <span style="color: #fbb6ce;">Serial</span>.<span style="color: #81e6d9;">begin</span>(<span style="color: #f6e05e;">115200</span>);
  <span style="color: #81e6d9;">pinMode</span>(<span style="color: #fbb6ce;">RED_PIN</span>, <span style="color: #f687b3;">OUTPUT</span>);
  <span style="color: #81e6d9;">pinMode</span>(<span style="color: #fbb6ce;">BUZZER_PIN</span>, <span style="color: #f687b3;">OUTPUT</span>);
  <span style="color: #81e6d9;">pinMode</span>(<span style="color: #fbb6ce;">Capteur_eau</span>, <span style="color: #f687b3;">INPUT</span>);

  <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">RED_PIN</span>, <span style="color: #f687b3;">LOW</span>);
  <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">BUZZER_PIN</span>, <span style="color: #f687b3;">LOW</span>);
}

<span style="color: #63b3ed;">void</span> <span style="color: #81e6d9;">loop</span>() {
  <span style="color: #63b3ed;">int</span> <span style="color: #fbb6ce;">Valeur_niveau_eau</span> = <span style="color: #81e6d9;">analogRead</span>(<span style="color: #fbb6ce;">Capteur_eau</span>);
  <span style="color: #fbb6ce;">Serial</span>.<span style="color: #81e6d9;">println</span>(<span style="color: #9ae6b4;">"Le niveau d'eau est : "</span>);
  <span style="color: #fbb6ce;">Serial</span>.<span style="color: #81e6d9;">println</span>(<span style="color: #fbb6ce;">Valeur_niveau_eau</span>);

  <span style="color: #f687b3;">if</span> (<span style="color: #fbb6ce;">Valeur_niveau_eau</span> >= <span style="color: #f6e05e;">300</span>) {
    <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">RED_PIN</span>, <span style="color: #f687b3;">HIGH</span>);   <span style="color: #68d391;">// Allume la led rouge si le niveau d'eau dépasse 300</span>
    <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">BUZZER_PIN</span>, <span style="color: #f687b3;">HIGH</span>); <span style="color: #68d391;">// Déclenche le buzzer si le niveau d'eau dépasse 300</span>
  } <span style="color: #f687b3;">else</span> {
    <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">RED_PIN</span>, <span style="color: #f687b3;">LOW</span>);
    <span style="color: #81e6d9;">digitalWrite</span>(<span style="color: #fbb6ce;">BUZZER_PIN</span>, <span style="color: #f687b3;">LOW</span>);
  }
}
</code>
                </pre>
                        </div>
                    </div>
                </div>
                <p><strong>Seuil d'alerte choisi:</strong> 300 (soit environ 78% de la valeur maximale de 383)</p>

                <div class="media-gallery">
                    <div class="media-item">
                        <div class="img-container">
                            <img src="images/mon_montage.jpg" alt="Photo de mon montage complet avec LED et buzzer">
                            <div class="caption">🔧 Mon montage complet avec LED rouge et buzzer</div>
                        </div>
                    </div>
                    <div class="media-item">
                        <div class="img-container">
                            <video controls style="max-width: 100%; height: auto;">
                                <source src="images/video2.mp4" type="video/mp4">
                                Votre navigateur ne supporte pas la lecture de vidéos.
                            </video>
                            <div class="caption">🎬 Démonstration du système d'alerte</div>
                        </div>
                    </div>
                </div>

                <p><strong>Résultats:</strong> Le système fonctionne correctement - la LED s'allume et le buzzer se déclenche dès que la valeur dépasse 300, confirmant la fiabilité des mesures du capteur.</p>
            </div>
            
            <h3>Expérimentation 3: Analyse de la sensibilité et stabilité</h3>
            <div class="card">
                <p><strong>Objectif:</strong> Analyser la sensibilité du capteur et sa stabilité dans le temps.</p>
                <p><strong>Contexte:</strong> Tests prolongés pour observer le comportement du capteur dans différentes conditions et sa réaction aux facteurs environnementaux.</p>

                <div class="img-container">
                    <video controls style="max-width: 100%; height: auto;">
                        <source src="images/video3.mp4" type="video/mp4">
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                    <div class="caption">Vidéo démonstration - Test de sensibilité du capteur</div>
                </div>

                <div class="img-container">
                    <video controls style="max-width: 100%; height: auto;">
                        <source src="images/video4.mp4" type="video/mp4">
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                    <div class="caption">Vidéo démonstration - Analyse de stabilité</div>
                </div>

                <p><strong>Observations techniques mesurées:</strong></p>
                <ul>
                    <li><strong>Plage de mesure effective:</strong> 0 à 383 (valeurs réellement observées)</li>
                    <li><strong>Réponse progressive:</strong> 0 (sec) → 185-200 (mi-immersion) → 383 (total)</li>
                    <li><strong>Longueur active:</strong> 15 cm de capteur utilisable</li>
                    <li><strong>Sensibilité élevée:</strong> Le capteur réagit même à l'humidité corporelle</li>
                    <li><strong>Variance temporelle:</strong> Les résultats varient avec la durée d'expérimentation</li>
                    <li><strong>Sensibilité environnementale:</strong> Réaction aux conditions ambiantes (température, humidité)</li>
                    <li><strong>Reproductibilité limitée:</strong> Les mesures peuvent varier selon les conditions</li>
                </ul>
                <p><strong>Analyse:</strong> La valeur maximale de 383 correspond à la saturation du capteur dans l'eau utilisée. La progression linéaire des valeurs (0 → ~190 → 383) confirme une réponse proportionnelle au niveau d'immersion. La haute sensibilité est un avantage pour la détection, mais peut causer des faux positifs dans des environnements variables.</p>
            </div>
        </section>

        <!-- Section avis -->
        <section class="section" id="avis">
            <h2>Avis sur la technologie</h2>
            
            <h3>Longévité</h3>
            <p>Le capteur montre une longévité correcte pour un usage occasionnel, mais peut être sujet à l'oxydation des électrodes en cas d'immersion prolongée.</p>
            
            <h3>Stabilité</h3>
            <p>Les mesures montrent une variance notable dans le temps. Le capteur est très sensible aux conditions environnementales (humidité, température) et peut même réagir à l'humidité corporelle. Cette sensibilité élevée peut causer des fluctuations dans les lectures, particulièrement lors d'expérimentations prolongées.</p>
            
            <h3>Efficacité</h3>
            <p>Le capteur est efficace pour détecter la présence d'eau avec une plage de valeurs de 0 à 383. La réponse est progressive (0 → 185-200 → 383) et permet une détection fiable des niveaux d'eau sur ses 15 cm de longueur. Le système d'alerte avec LED et buzzer fonctionne parfaitement pour signaler les dépassements de seuil.</p>
            
            <h3>Maintenabilité</h3>
            <p>Facile à remplacer et peu coûteux, ce qui compense sa durée de vie limitée dans des conditions difficiles.</p>
            
            <h3>Robustesse à la manipulation</h3>
            <p>Le capteur d'eau nécessite une manipulation délicate et des précautions particulières :</p>
            <ul>
                <li><strong>Fragilité des électrodes :</strong> Les pistes conductrices peuvent se plier, se fissurer ou se casser facilement lors de manipulations brusques</li>
                <li><strong>Sensibilité aux contacts :</strong> Éviter de toucher directement les électrodes avec les doigts car l'humidité corporelle peut fausser les mesures</li>
                <li><strong>Précautions de branchement :</strong> S'assurer que les connexions sont bien sécurisées pour éviter les faux contacts</li>
                <li><strong>Stockage :</strong> Conserver dans un endroit sec pour éviter l'oxydation prématurée</li>
                <li><strong>Nettoyage :</strong> Nettoyer délicatement après usage pour éviter l'accumulation de résidus qui pourraient affecter la conductivité</li>
            </ul>
            <p><strong>Recommandation :</strong> Manipuler avec précaution et prévoir des capteurs de rechange pour les projets à long terme.</p>
            
            <h3>Avantages et inconvénients</h3>
            <table>
                <tr>
                    <th>Avantages</th>
                    <th>Inconvénients</th>
                </tr>
                <tr>
                    <td>Prix très abordable</td>
                    <td>Sensible à l'oxydation</td>
                </tr>
                <tr>
                    <td>Facile à utiliser avec Arduino</td>
                    <td>Les mesures peuvent être influencées par la minéralisation de l'eau</td>
                </tr>
                <tr>
                    <td>Très haute sensibilité de détection</td>
                    <td>Sensible aux variations environnementales</td>
                </tr>
                <tr>
                    <td>Détection fiable de la présence d'eau</td>
                    <td>Variance dans les mesures prolongées</td>
                </tr>
            </table>
            
            <h3>Avis final</h3>
            <div class="note">
                <p>Basé sur mes expérimentations pratiques, ce capteur d'eau de 15 cm est une solution économique et fonctionnelle pour des applications de surveillance de niveau d'eau. Les tests ont confirmé son bon fonctionnement avec une plage de valeurs de 0 à 383 et une réponse progressive (mi-immersion à ~190, total à 383). L'intégration avec des systèmes d'alerte (LED, buzzer) est simple et efficace. Pour des projets éducatifs ou des applications domestiques simples, ce capteur répond aux besoins de base. Cependant, sa sensibilité aux conditions environnementales et la variance temporelle des mesures limitent son usage pour des applications nécessitant une précision constante ou des environnements variables.</p>
            </div>
        </section>

        <!-- Section sources -->
        <section class="section" id="sources">
            <h2>Sources d'information</h2>

            <h3>Documentation et sites web</h3>
            <ul>
                <li><a href="https://docs.arduino.cc/hardware/mega-2560/" target="_blank">Arduino Mega 2560 (Documentation technique du fabricant)</a></li>
                <li><a href="https://www.arduino.cc/" target="_blank">Site officiel Arduino</a></li>
                <li><a href="https://www.amazon.ca/dp/B01EWNUUUA" target="_blank">Kit Elegoo Arduino Mega 2560 - Amazon Canada (matériel utilisé)</a></li>
                <li><a href="https://arduino-france.site/capteur-deau/#2" target="_blank">Arduino France - Capteur d'eau (schéma de montage de référence)</a></li>
                <li><a href="https://www.instructables.com/Testing-Water-Level-Sensor-Arduino-UNO-Water-Level/" target="_blank">Instructables - Exemples montage + code</a></li>
                <li><a href="https://projecthub.arduino.cc/daryllqayyim/water-sensor-basic-13d33f" target="_blank">Arduino Project Hub - Water Sensor Basic</a></li>
            </ul>

            <h3>Vidéos tutorielles consultées</h3>
            <ul>
                <li><a href="https://www.youtube.com/watch?v=n7WRi5U5lQk" target="_blank">"Arduino water level Sensor Tutorial"</a> - theGeekPub (12 mai 2019)</li>
                <li><a href="https://www.youtube.com/watch?v=6s4Sh8badNM" target="_blank">"[arduino] Mesurer le niveau d'eau avec Arduino"</a> - Comprendre l'électronique (16 juillet 2019)</li>
                <li><a href="https://www.youtube.com/watch?v=WzYmmu17svE" target="_blank">"Capteur de niveau d'eau analogique Arduino"</a> - Mr Automatisme et domotique (12 juin 2020)</li>
            </ul>
        </section>
    </main>

    <!-- Pied de page -->
    <footer class="footer">
        <p>© 2025 - Projet de veille technologique - Capteur d'eau</p>
        <p>Réalisé par <strong>Divin Koffi Sogbadji</strong> - Session 5 Technique de l'informatique</p>
        <p><strong>Cégep Garneau</strong> - Professeur: Jean-Philippe Boucher</p>
    </footer>

    <script>
        // Script simple pour la navigation
        window.onscroll = function() {stickyNav()};

        var navbar = document.querySelector(".navbar");
        var sticky = navbar.offsetTop;

        function stickyNav() {
            if (window.pageYOffset >= sticky) {
                navbar.classList.add("sticky");
            } else {
                navbar.classList.remove("sticky");
            }
        }

        // Gestion de l'état actif des liens de navigation
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.navbar a');
            const sections = document.querySelectorAll('.section');

            // Fonction pour mettre à jour le lien actif
            function updateActiveLink() {
                let current = '';

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 200) {
                        current = section.getAttribute('id');
                    }
                });

                // Si on est en haut de la page, activer "Accueil"
                if (window.pageYOffset < 100) {
                    current = '';
                }

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current ||
                        (current === '' && link.getAttribute('href') === '#')) {
                        link.classList.add('active');
                    }
                });
            }

            // Gestion du clic sur les liens
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Retirer la classe active de tous les liens
                    navLinks.forEach(l => l.classList.remove('active'));
                    // Ajouter la classe active au lien cliqué
                    this.classList.add('active');
                });
            });

            // Mettre à jour l'état actif lors du scroll
            window.addEventListener('scroll', updateActiveLink);

            // Initialiser l'état actif
            updateActiveLink();
        });
    </script>
</body>
</html>